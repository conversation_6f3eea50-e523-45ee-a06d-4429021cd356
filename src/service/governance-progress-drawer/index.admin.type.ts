import { AuthorTypeEnum } from '@src/constant';

export interface IDescribeRiskInstancesInMapParams {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  MapId: string;
  Limit: number;
  Offset: number;
  Filters: {
    Name: string;
    Values: string[]
  }[];
  TagList?: Array<{
    Key: string;
    Value: string;
  }>;
}

export interface IDescribeRiskInstancesInMapResult {
  TotalCount: number;
  RiskInstanceList: {
    InstanceId: string;
    HighRiskCount: number;
    MediumRiskCount: number;
    Tag: {
      Key: string;
      Value: string;
    }[];
    ClaimPerson: string;
    Url: string;
    Region: string;
    Product: string;
    NodeId: string;
    TaskId: string;
  }[];
}

export interface ICreateSubscriptionEmailV2Params {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  Email?: string;
  UserName?: string;
  SubUin: string;
}

export interface IUpdateSubscriptionEmailV2Params {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  Id: number;
  Email: string;
  UserName: string;
  SubUin: string;
}

export interface IUpdateInstanceToClaimedStatusInMapParams {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  MapId: string;
  Operate: string;
  MapRiskInstanceList: {
    InstanceId: string;
    Product: string
  } [];
  ClaimPerson: string;
  ClaimUin: string;
}

export interface IUpdateInstanceToIgnoredStatusInMapParams {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  MapId: string;
  Operate: string;
  MapRiskInstanceList: {
    InstanceId: string;
    Product: string
  } [];
  Person: string;
  Reason: string;
}

export interface IAdminDescribeRiskManageSubjectListParams {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  ArchId: string;
  Limit: number;
  Offset: number;
}

export interface IAdminScanRiskManageSubject {
  /** 主题 ID (int64) */
  Id: number;
  /** 标题 */
  Title: string;
  /** 描述 */
  Description: string;
  /** 来源（1: Agent, 2: 自定义） */
  Source: number;
  /** 创建时间 (格式: YYYY-MM-DD HH:mm:ss) */
  CreateTime: string;
  /** 会话ID (用于查看Agent生成内容) */
  SessionId: string;
  /** 聊天ID (用于查看Agent生成内容) */
  ChatId: string;
  /** 作者 */
  Author: string;
}

export interface IAdminDescribeRiskManageSubjectListResponse {
  Total: number;
  Items: IAdminScanRiskManageSubject[];
}

export interface IAdminDescribeRiskManageInstanceListParams {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  /** 主题 ID */
  SubjectId?: number;
  /** 架构图 ID */
  ArchId?: string;
  /** 每页行数 */
  Limit?: number;
  /** 偏移量 */
  Offset?: number;
  /** 支持按实例ID(InsId) | 实例名称(Name) ｜ 标签(TagList) ｜ 标签键(TagName)筛选 */
  Filters?: {
    Name: string;
    Values: string[];
  }[];
  /** 通过标签过滤实例 */
  TagList?: {
    Key: string;
    Value: string;
  }[];
}

export interface IAdminArchScanRiskInstanceItem {
  /** 实例ID */
  InstanceId: string;
  // 根据实际需要可以添加更多字段
}

export interface IAdminDescribeRiskManageInstanceListResponse {
  /** 总数 */
  Total: number;
  /** 实例列表 */
  InstanceItems: IAdminArchScanRiskInstanceItem[];
}

export interface IAdminCreateRiskManageInstanceParams {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  /** 主题 ID */
  SubjectId: number;
  /** 实例 ID */
  InstanceId: string;
  /** 架构图ID */
  ArchId: string;
  /** 节点 UUID */
  NodeUuid?: string;
}

export interface IAdminScanRiskManageInstance {
  /** 节点 UUID */
  NodeUuid: string;
  /** 实例 ID */
  InstanceId: string;
}

export interface IAdminUpdateRiskManageSubjectParams {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  /** 架构图 ID */
  ArchId: string;
  /** 主题标题 */
  Title: string;
  /** 描述 */
  Description: string;
  /** 治理项 ID,更新时必传 */
  Id?: number;
  /** 来源（1：agent,2:自定义） */
  Source?: number;
  /** 风险实例信息，创建必传 */
  InstanceItems?: IAdminScanRiskManageInstance[];
  /** 会话 ID，创建必传 */
  SessionId?: string;
  /** Agent 会话 ID，创建必传 */
  ChatId?: string;
  /** 创建人名称 */
  Author?: string;
  AuthorType?: AuthorTypeEnum;
}

export interface IAdminUpdateRiskManageSubjectResponse {
  /** 风险主题 ID */
  Id: number;
}

export interface IAdminDeleteRiskManageSubjectParams {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  /** 治理项 ID */
  Base64Id?: number;
  /** 架构图 ID */
  ArchId?: string;
  Username?: string;
}

export interface IAdminDescribeInsightMessageParams {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  /** 架构图ID */
  ArchId?: string;
  /** 会话ID */
  SessionId?: string;
  /** 对话ID */
  ChatId?: string;
}

export interface IAdminInsightMessage {
  /** 消息内容 */
  Content: string;
  /** 消息类型 */
  ContentType: string;
  /** 创建时间 */
  CreateTime: string;
}

export interface IAdminDescribeInsightMessageResponse {
  /** 消息数组 */
  Messages: IAdminInsightMessage[];
}

export interface IAdminDescribeArchSvgDataParams {
  AppId: number;
  CustomerAppId: number;
  Uin: string;
  SubAccountUin: string;
  /** 架构图ID */
  ArchId: string;
  /** 版本ID，不传默认为当前版本 */
  VersionId?: number;
  /** 是否3d图，不传默认2d */
  IsThreeDimension?: boolean;
}

export interface IAdminDescribeArchSvgDataResponse {
  /** 架构ID */
  ArchId: string;
  /** svg图片信息 */
  Svg: string;
}

export interface IDescribeInsightGovernTopicParams {
  AppId: number;
  Uin: string;
  SubAccountUin: string;
  /** 架构图ID */
  ArchId: string;
  /** 会话ID */
  SessionId: string;
  /** 对话ID */
  ChatId: string;
}

export interface IDescribeInsightGovernTopicResponse {
  /** 主题内容 */
  Content: string;
  CreateTime: string;
}
